import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { StateElectionController } from './state-election.controller';
import { StateElectionService } from './state-election.service';
import { StateElection } from './entity/state-election.entity';
import { State } from '../state/entity/state.entity';
import { User } from '../user/entity/user.entity';
import { Party } from '../party/entity/party.entity';
import { StateModule } from '../state/state.module';
import { NotificationModule } from '../notification/notification.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([StateElection, State, User, Party]),
    ScheduleModule.forRoot(),
    StateModule,
    NotificationModule,
    AuthModule,
  ],
  controllers: [StateElectionController],
  providers: [StateElectionService],
  exports: [StateElectionService],
})
export class StateElectionModule {}