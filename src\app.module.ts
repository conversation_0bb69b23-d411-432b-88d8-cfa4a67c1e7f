import { PartyModule } from './party/party.module';
import { RegionModule } from './region/region.module';
import { MailModule } from './mail/mail.module';
import { UserModule } from './user/user.module';
import { NotificationModule } from './notification/notification.module';
import { PaymentModule } from './payment/payment.module';
import { TravelModule } from './travel/travel.module';
import { ChatModule } from './chat/chat.module';
import { QueueModule } from './queue/queue.module';
import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import typeorm from './database/typeorm';
import { AuthModule } from './auth/auth.module';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import { AuthGuard } from './common/guards/auth.guard';
import { AllExceptionFilter } from './common/all-exceptions.filter';
import { TokenRefreshMiddleware } from './common/middlewares/token-refresh.middleware';
import { FactoryModule } from './factory/factory.module';
import { WorkSessionModule } from './work-session/work-session.module';
import { StateModule } from './state/state.module';
import { StateElectionModule } from './state-election/state-election.module';
import { WarModule } from './war/war.module';
import { ScheduleModule } from '@nestjs/schedule';
import { SharedModule } from './shared/shared.module';
import { StorageModule } from './storage/storage.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    SharedModule,
    StorageModule,
    FactoryModule,
    WorkSessionModule,
    PartyModule,
    RegionModule,
    MailModule,
    AuthModule,
    UserModule,
    StateModule,
    StateElectionModule,
    WarModule,
    NotificationModule,
    PaymentModule,
    TravelModule,
    ChatModule,
    QueueModule,
    ConfigModule.forRoot({ isGlobal: true, load: [typeorm] }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) =>
        configService.get('typeorm') as TypeOrmModuleOptions,
    }),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionFilter,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(TokenRefreshMiddleware)
      .exclude(
        { path: 'auth/login', method: RequestMethod.POST },
        { path: 'auth/logout', method: RequestMethod.GET },
      )
      .forRoutes('*');
  }
}
