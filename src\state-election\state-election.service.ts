import {
  Injectable,
  BadRequestException,
  NotFoundException,
  OnApplicationBootstrap,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { SchedulerRegistry } from '@nestjs/schedule';
import { StateElection, ElectionStatus } from './entity/state-election.entity';
import { State } from '../state/entity/state.entity';
import { User } from '../user/entity/user.entity';
import { Party } from '../party/entity/party.entity';
import { VoteDto } from './dto/vote.dto';
import { StateService } from '../state/state.service';
import { NotificationService } from '../notification/notification.service';
import { NotificationType } from '../notification/entity/notification.entity';

@Injectable()
export class StateElectionService implements OnApplicationBootstrap {
  private readonly ELECTION_DURATION_MS = 1 * 24 * 60 * 60 * 1000;
  private readonly ELECTION_CYCLE_DAYS = 5; // Elections every 5 days
  private readonly logger = new Logger(StateElectionService.name);

  constructor(
    @InjectRepository(StateElection)
    private electionRepository: Repository<StateElection>,
    @InjectRepository(State)
    private stateRepository: Repository<State>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Party)
    private partyRepository: Repository<Party>,
    private stateService: StateService,
    private notificationService: NotificationService,
    private schedulerRegistry: SchedulerRegistry,
  ) {}

  async onApplicationBootstrap() {
    try {
      // Check for any elections that should be running but aren't
      await this.checkPendingElections();
      // Restore election end schedules
      await this.restoreElectionEndSchedules();
    } catch (error) {
      this.logger.error(`Error during bootstrap: ${error.message}`, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async checkPendingElections() {
    try {
      this.logger.log('Checking for pending elections');
      // Find all states
      const states = await this.stateRepository.find();

      for (const state of states) {
        // Find the most recent election for this state
        const lastElection = await this.electionRepository.findOne({
          where: { state: { id: state.id } },
          order: { createdAt: 'DESC' },
        });

        const now = new Date();

        // If no previous election or last election was more than ELECTION_CYCLE_DAYS ago
        if (!lastElection ||
            (lastElection.status === ElectionStatus.COMPLETED &&
             now.getTime() - lastElection.endedAt.getTime() > this.ELECTION_CYCLE_DAYS * 24 * 60 * 60 * 1000)) {
          this.logger.log(`Scheduling new election for state: ${state.id}`);
          await this.scheduleElection(state.id);
        }
      }
    } catch (error) {
      this.logger.error(`Error checking pending elections: ${error.message}`, error.stack);
    }
  }

  async scheduleElection(stateId: string): Promise<StateElection> {
    try {
      const state = await this.stateRepository.findOne({
        where: { id: stateId },
        relations: ['regions', 'leader'],
      });

      if (!state) {
        throw new NotFoundException(`State with ID ${stateId} not found`);
      }

      // Create a new election
      const election = this.electionRepository.create({
        state,
        status: ElectionStatus.PENDING,
        candidates: [],
        voters: [],
      });

      const savedElection = await this.electionRepository.save(election);
      this.logger.log(`Created new election with ID: ${savedElection.id} for state: ${state.id}`);

      // Start the election immediately
      return this.startElection(savedElection.id);
    } catch (error) {
      this.logger.error(`Error scheduling election for state ${stateId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async startElection(electionId: string): Promise<StateElection> {
    try {
      const election = await this.electionRepository.findOne({
        where: { id: electionId },
        relations: ['state', 'state.regions'],
      });

      if (!election) {
        throw new NotFoundException(`Election with ID ${electionId} not found`);
      }

      if (election.status !== ElectionStatus.PENDING) {
        throw new BadRequestException('Election is not in pending status');
      }

      // Find all parties in the state's regions and add their leaders as candidates
      const candidates: { userId: number; username: string; votes: number }[] = [];

      for (const region of election.state.regions) {
        // Find all active parties in this region
        const parties = await this.partyRepository.find({
          where: {
            region: { id: region.id },
            isActive: true
          },
          relations: ['leader'],
        });

        for (const party of parties) {
          if (party.leader && !candidates.some(c => c.userId === party.leader.id)) {
            candidates.push({
              userId: party.leader.id,
              username: party.leader.username,
              votes: 0,
            });
          }
        }
      }

      // Update election with proper timing
      const startTime = new Date();
      const endTime = new Date(startTime.getTime() + this.ELECTION_DURATION_MS);

      election.status = ElectionStatus.ACTIVE;
      election.startedAt = startTime;
      election.endedAt = endTime;
      election.candidates = candidates;

      const savedElection = await this.electionRepository.save(election);
      this.logger.log(`Started election ${electionId} with ${candidates.length} candidates`);

      // Schedule election end
      const timeout = setTimeout(async () => {
        try {
          await this.endElection(savedElection.id);
        } catch (error) {
          this.logger.error(`Error ending election ${savedElection.id}: ${error.message}`, error.stack);
        }
      }, this.ELECTION_DURATION_MS);

      this.schedulerRegistry.addTimeout(`election_end_${savedElection.id}`, timeout);

      // Notify all users in the state about the election
      // await this.notifyAboutElection(savedElection);

      return savedElection;
    } catch (error) {
      this.logger.error(`Error starting election ${electionId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async vote(userId: number, electionId: string, voteDto: VoteDto): Promise<StateElection> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['region', 'region.state'],
      });

      if (!user || !user.region || !user.region.state) {
        throw new BadRequestException('You must be part of a state to vote');
      }

      // Simple account eligibility check
      if (user.level < 5) {
        throw new BadRequestException('Your account must be at least level 5 to vote in elections');
      }

      if (user.intelligence < 20) {
        throw new BadRequestException('You must have at least 20 intelligence to vote in elections');
      }

      const election = await this.electionRepository.findOne({
        where: { id: electionId, state: { id: user.region.state.id } },
      });

      if (!election) {
        throw new NotFoundException(`Election with ID ${electionId} not found`);
      }

      if (election.status !== ElectionStatus.ACTIVE) {
        throw new BadRequestException('Election is not active');
      }

      // Check if user already voted in this election
      if (election.voters.some(v => v.userId === userId)) {
        throw new BadRequestException('You have already voted in this election');
      }

      // Check if user has voted in any active election across all states
      const activeElections = await this.electionRepository.find({
        where: { status: ElectionStatus.ACTIVE },
      });

      for (const activeElection of activeElections) {
        if (activeElection.id !== electionId && 
            activeElection.voters.some(v => v.userId === userId)) {
          throw new BadRequestException('You have already voted in another active election');
        }
      }

      // Check if candidate exists
      const candidateIndex = election.candidates.findIndex(c => c.userId === voteDto.candidateId);
      if (candidateIndex === -1) {
        throw new BadRequestException('Invalid candidate');
      }

      // Record vote
      election.candidates[candidateIndex].votes += 1;
      election.voters.push({ userId, votedFor: voteDto.candidateId });

      const savedElection = await this.electionRepository.save(election);
      this.logger.log(`User ${userId} voted for candidate ${voteDto.candidateId} in election ${electionId}`);

      return savedElection;
    } catch (error) {
      this.logger.error(`Error processing vote for election ${electionId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async endElection(electionId: string): Promise<StateElection> {
    try {
      const election = await this.electionRepository.findOne({
        where: { id: electionId },
        relations: ['state'],
      });

      if (!election) {
        throw new NotFoundException(`Election with ID ${electionId} not found`);
      }

      if (election.status !== ElectionStatus.ACTIVE) {
        throw new BadRequestException('Election is not active');
      }

      // Find the winner (candidate with most votes)
      let winnerIndex = -1;
      let maxVotes = -1;

      for (let i = 0; i < election.candidates.length; i++) {
        if (election.candidates[i].votes > maxVotes) {
          maxVotes = election.candidates[i].votes;
          winnerIndex = i;
        }
      }

      // Update election status
      election.status = ElectionStatus.COMPLETED;
      election.endedAt = new Date();

      // If there's a winner, update the state leader
      if (winnerIndex !== -1) {
        const winnerId = election.candidates[winnerIndex].userId;
        const winner = await this.userRepository.findOne({
          where: { id: winnerId },
        });

        if (winner) {
          election.winner = winner;
          await this.stateService.changeLeader(election.state.id, winnerId);
          this.logger.log(`Election ${electionId} ended. Winner: ${winner.username} (ID: ${winnerId})`);

          // Notify about the new leader
          // await this.notifyAboutElectionResults(election, winner);
        }
      } else {
        this.logger.log(`Election ${electionId} ended with no winner`);
      }

      // Clean up the timeout
      try {
        this.schedulerRegistry.deleteTimeout(`election_end_${electionId}`);
      } catch (error) {
        this.logger.warn(`Error clearing timeout for election ${electionId}: ${error.message}`);
      }

      return this.electionRepository.save(election);
    } catch (error) {
      this.logger.error(`Error ending election ${electionId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getActiveElectionForState(stateId: string): Promise<StateElection | null> {
    try {
      return this.electionRepository.findOne({
        where: { state: { id: stateId }, status: ElectionStatus.ACTIVE },
        relations: ['state','state.regions'],
      });
    } catch (error) {
      this.logger.error(`Error getting active election for state ${stateId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getElectionHistory(stateId: string, limit: number = 10): Promise<StateElection[]> {
    try {
      return this.electionRepository.find({
        where: { state: { id: stateId }, status: ElectionStatus.COMPLETED },
        relations: ['winner', 'state'],
        order: { endedAt: 'DESC' },
        take: limit,
      });
    } catch (error) {
      this.logger.error(`Error getting election history for state ${stateId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async notifyAboutElection(election: StateElection): Promise<void> {
    try {
      // Get all users in the state
      const state = await this.stateRepository.findOne({
        where: { id: election.state.id },
        relations: ['regions'],
      });

      if (!state) return;

      for (const region of state.regions) {
        const users = await this.userRepository.find({
          where: { region: { id: region.id } },
        });

        for (const user of users) {
          await this.notificationService.createNotification(
            user,
            NotificationType.STATE_ELECTION,
            'State Election Started',
            `An election has started in ${state.name}. Cast your vote for the next leader!`,
            election.id,
            'election',
            false,
          );
        }
      }
      this.logger.log(`Sent election notifications for election ${election.id}`);
    } catch (error) {
      this.logger.error(`Error sending election notifications: ${error.message}`, error.stack);
    }
  }

  private async notifyAboutElectionResults(election: StateElection, winner: User): Promise<void> {
    try {
      // Get all users in the state
      const state = await this.stateRepository.findOne({
        where: { id: election.state.id },
        relations: ['regions'],
      });

      if (!state) return;

      for (const region of state.regions) {
        const users = await this.userRepository.find({
          where: { region: { id: region.id } },
        });

        for (const user of users) {
          await this.notificationService.createNotification(
            user,
            NotificationType.STATE_ELECTION,
            'Election Results',
            `${winner.username} has been elected as the new leader of ${state.name}.`,
            election.id,
            'election',
            false,
          );
        }
      }
      this.logger.log(`Sent election results notifications for election ${election.id}`);
    } catch (error) {
      this.logger.error(`Error sending election results notifications: ${error.message}`, error.stack);
    }
  }

  async restoreElectionEndSchedules() {
    try {
      this.logger.log('Restoring election end schedules');
      const activeElections = await this.electionRepository.find({
        where: { status: ElectionStatus.ACTIVE },
      });

      for (const election of activeElections) {
        if (!election.startedAt) {
          this.logger.warn(`Active election ${election.id} has no start date, skipping`);
          continue;
        }

        const now = new Date().getTime();
        const electionEndTime = election.startedAt.getTime() + this.ELECTION_DURATION_MS;
        const remainingTime = electionEndTime - now;

        if (remainingTime <= 0) {
          // Election should have ended already
          this.logger.log(`Election ${election.id} should have ended already, ending now`);
          await this.endElection(election.id);
        } else {
          // Reschedule the remaining time
          this.logger.log(`Rescheduling election ${election.id} to end in ${Math.round(remainingTime / 1000 / 60)} minutes`);
          const timeout = setTimeout(async () => {
            try {
              await this.endElection(election.id);
            } catch (error) {
              this.logger.error(`Error ending election ${election.id}: ${error.message}`, error.stack);
            }
          }, remainingTime);

          this.schedulerRegistry.addTimeout(`election_end_${election.id}`, timeout);
        }
      }
    } catch (error) {
      this.logger.error(`Error restoring election end schedules: ${error.message}`, error.stack);
    }
  }

  // Testing methods
  async restartElectionsForTesting(stateId?: string): Promise<void> {
    try {
      this.logger.log('Restarting elections for testing purposes');

      let statesToRestart: State[];

      if (stateId) {
        const state = await this.stateRepository.findOne({
          where: { id: stateId },
          relations: ['regions'],
        });
        if (!state) {
          throw new NotFoundException(`State with ID ${stateId} not found`);
        }
        statesToRestart = [state];
      } else {
        statesToRestart = await this.stateRepository.find({
          relations: ['regions'],
        });
      }

      for (const state of statesToRestart) {
        // End any active elections
        const activeElection = await this.electionRepository.findOne({
          where: { state: { id: state.id }, status: ElectionStatus.ACTIVE },
        });

        if (activeElection) {
          this.logger.log(`Ending active election ${activeElection.id} for state ${state.id}`);
          try {
            this.schedulerRegistry.deleteTimeout(`election_end_${activeElection.id}`);
          } catch (error) {
            // Timeout might not exist, that's ok
          }

          activeElection.status = ElectionStatus.COMPLETED;
          activeElection.endedAt = new Date();
          await this.electionRepository.save(activeElection);
        }

        // Create and start a new election immediately
        this.logger.log(`Creating new election for state ${state.id}`);
        await this.scheduleElection(state.id);
      }

      this.logger.log('Elections restart completed');
    } catch (error) {
      this.logger.error(`Error restarting elections: ${error.message}`, error.stack);
      throw error;
    }
  }

  async forceEndElection(electionId: string): Promise<StateElection> {
    try {
      this.logger.log(`Force ending election ${electionId} for testing`);
      return this.endElection(electionId);
    } catch (error) {
      this.logger.error(`Error force ending election ${electionId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
