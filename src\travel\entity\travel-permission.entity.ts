import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { Region } from '../../region/entity/region.entity';
import { State } from '../../state/entity/state.entity';

export enum TravelPermissionStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Entity()
export class TravelPermission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  user: User;

  @ManyToOne(() => Region)
  sourceRegion: Region;

  @ManyToOne(() => Region)
  destinationRegion: Region;

  @ManyToOne(() => State)
  destinationState: State;

  @Column({
    type: 'enum',
    enum: TravelPermissionStatus,
    default: TravelPermissionStatus.PENDING,
  })
  status: TravelPermissionStatus;

  @Column({ type: 'text', nullable: true })
  reason: string;

  @Column({ type: 'text', nullable: true })
  responseMessage: string;

  @ManyToOne(() => User, { nullable: true })
  respondedBy: User;

  @Column({ type: 'timestamp', nullable: true })
  respondedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
