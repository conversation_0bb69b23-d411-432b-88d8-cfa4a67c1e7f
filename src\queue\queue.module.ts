import { Module, forwardRef } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AutoActionQueue } from './auto-action.queue';
import { AutoActionWorker } from './auto-action.worker';
import { BullMQAutoActionService } from '../shared/bullmq-auto-action.service';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        connection: {
          host: configService.get('REDIS_HOST') || 'localhost',
          port: configService.get('REDIS_PORT') || 6379,
          password: configService.get('REDIS_PASSWORD'),
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: 'auto-actions',
    }),
    forwardRef(() => UserModule),
  ],
  providers: [AutoActionQueue, AutoActionWorker, BullMQAutoActionService],
  exports: [AutoActionQueue, BullMQAutoActionService],
})
export class QueueModule {}
