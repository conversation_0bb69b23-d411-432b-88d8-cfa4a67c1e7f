import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1749037903779 implements MigrationInterface {
    name = 'Migrations1749037903779'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."war_history_finalstatus_enum" RENAME TO "war_history_finalstatus_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."war_history_finalstatus_enum" AS ENUM('pending', 'ground_phase', 'revolution_phase', 'ended')`);
        await queryRunner.query(`ALTER TABLE "war_history" ALTER COLUMN "finalStatus" TYPE "public"."war_history_finalstatus_enum" USING "finalStatus"::"text"::"public"."war_history_finalstatus_enum"`);
        await queryRunner.query(`DROP TYPE "public"."war_history_finalstatus_enum_old"`);
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_bc096b4e18b1f9508197cd98066"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8051177e63e114cb9e78db7f0b"`);
        await queryRunner.query(`ALTER TABLE "message" ALTER COLUMN "senderId" SET NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_8051177e63e114cb9e78db7f0b" ON "message" ("senderId", "createdAt") `);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_bc096b4e18b1f9508197cd98066" FOREIGN KEY ("senderId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_bc096b4e18b1f9508197cd98066"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8051177e63e114cb9e78db7f0b"`);
        await queryRunner.query(`ALTER TABLE "message" ALTER COLUMN "senderId" DROP NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_8051177e63e114cb9e78db7f0b" ON "message" ("createdAt", "senderId") `);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_bc096b4e18b1f9508197cd98066" FOREIGN KEY ("senderId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`CREATE TYPE "public"."war_history_finalstatus_enum_old" AS ENUM('pending', 'ground_phase', 'ended')`);
        await queryRunner.query(`ALTER TABLE "war_history" ALTER COLUMN "finalStatus" TYPE "public"."war_history_finalstatus_enum_old" USING "finalStatus"::"text"::"public"."war_history_finalstatus_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."war_history_finalstatus_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."war_history_finalstatus_enum_old" RENAME TO "war_history_finalstatus_enum"`);
    }

}
