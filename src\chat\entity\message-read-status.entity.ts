import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  Index,
  Unique,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { Message } from './message.entity';

@Entity()
@Unique(['message', 'user'])
@Index(['user', 'readAt'])
export class MessageReadStatus {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Message, (message) => message.readStatuses, {
    onDelete: 'CASCADE',
  })
  message: Message;

  @ManyToOne(() => User)
  user: User;

  @Column({ type: 'timestamp', nullable: true })
  readAt: Date | null;

  @CreateDateColumn()
  createdAt: Date;
}
