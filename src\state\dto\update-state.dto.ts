import { IsString, IsUrl, IsOptional, IsBoolean } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateStateDto {
  @ApiPropertyOptional({
    description: 'New name for the state',
    example: 'United Federation of Azuria',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Updated description of the state',
    example: 'A federal republic with strong democratic institutions.',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: "Updated URL to the state's flag image",
    example: 'https://example.com/flags/united-azuria.png',
  })
  @IsUrl()
  @IsOptional()
  flagUrl?: string;

  @ApiPropertyOptional({
    description: 'Whether the state is active or inactive',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
