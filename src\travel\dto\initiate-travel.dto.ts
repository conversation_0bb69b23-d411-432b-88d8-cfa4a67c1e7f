import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class InitiateTravelDto {
  @ApiProperty({
    description: 'The ID of the destination region',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  destinationRegionId: string;

  @ApiProperty({
    description: 'Optional permission ID if travel requires permission',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  permissionId?: string;
}

export class TravelTimeEstimateDto {
  @ApiProperty({
    description: 'The ID of the source region',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  originRegionId: string;

  @ApiProperty({
    description: 'The ID of the destination region',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  destinationRegionId: string;
}

export class RequestTravelPermissionDto {
  @ApiProperty({
    description: 'The ID of the destination region',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  destinationRegionId: string;

  @ApiProperty({
    description: 'Reason for travel request',
    example: 'Business trip',
    required: false,
  })
  @IsString()
  @IsOptional()
  reason?: string;
}

export class RespondToPermissionRequestDto {
  @ApiProperty({
    description: 'Whether to approve or reject the request',
    example: true,
  })
  @IsNotEmpty()
  approve: boolean;

  @ApiProperty({
    description: 'Optional response message',
    example: 'Welcome to our state!',
    required: false,
  })
  @IsString()
  @IsOptional()
  responseMessage?: string;
}
