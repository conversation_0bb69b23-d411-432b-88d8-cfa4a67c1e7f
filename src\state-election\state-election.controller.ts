import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Request,
  UseGuards,
  HttpCode,
  HttpStatus,
  Logger,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { Request as ExpressRequest } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AuthGuard } from '../common/guards/auth.guard';
import { StateElectionService } from './state-election.service';
import { VoteDto } from './dto/vote.dto';
import { StateElection } from './entity/state-election.entity';

@ApiTags('state-elections')
@Controller('state-elections')
export class StateElectionController {
  private readonly logger = new Logger(StateElectionController.name);

  constructor(private readonly stateElectionService: StateElectionService) {}

  @UseGuards(AuthGuard)
  @Get('state/:stateId/active')
  @ApiOperation({ summary: 'Get active election for a state' })
  @ApiResponse({ status: 200, description: 'Returns the active election' })
  @ApiResponse({ status: 404, description: 'No active election found' })
  @ApiParam({ name: 'stateId', description: 'The ID of the state' })
  async getActiveElection(@Param('stateId') stateId: string): Promise<StateElection | null> {
    this.logger.log(`Getting active election for state ${stateId}`);
    return this.stateElectionService.getActiveElectionForState(stateId);
  }

  @UseGuards(AuthGuard)
  @Get('state/:stateId/history')
  @ApiOperation({ summary: 'Get election history for a state' })
  @ApiResponse({ status: 200, description: 'Returns election history' })
  @ApiParam({ name: 'stateId', description: 'The ID of the state' })
  @ApiQuery({ name: 'limit', description: 'Maximum number of elections to return', required: false, type: Number })
  async getElectionHistory(
    @Param('stateId') stateId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ): Promise<StateElection[]> {
    this.logger.log(`Getting election history for state ${stateId} with limit ${limit}`);
    return this.stateElectionService.getElectionHistory(stateId, limit);
  }

  @UseGuards(AuthGuard)
  @Post(':electionId/vote')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Vote in an election' })
  @ApiResponse({ status: 200, description: 'Vote recorded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request or already voted' })
  @ApiResponse({ status: 404, description: 'Election not found' })
  @ApiParam({ name: 'electionId', description: 'The ID of the election' })
  async vote(
    @Request() req: ExpressRequest & { user: { userId: number; username: string } },
    @Param('electionId') electionId: string,
    @Body() voteDto: VoteDto,
  ): Promise<StateElection> {
    this.logger.log(`User ${req.user.userId} voting in election ${electionId}`);
    return this.stateElectionService.vote(req.user.userId, electionId, voteDto);
  }

  // Testing endpoints
  // @UseGuards(AuthGuard)
  // @Post('restart')
  // @HttpCode(HttpStatus.OK)
  // @ApiOperation({ summary: 'Restart elections for testing (all states)' })
  // @ApiResponse({ status: 200, description: 'Elections restarted successfully' })
  // async restartAllElections(): Promise<{ message: string }> {
  //   this.logger.log('Restarting all elections for testing');
  //   await this.stateElectionService.restartElectionsForTesting();
  //   return { message: 'All elections restarted successfully' };
  // }

  // @UseGuards(AuthGuard)
  // @Post('restart/:stateId')
  // @HttpCode(HttpStatus.OK)
  // @ApiOperation({ summary: 'Restart election for specific state (testing)' })
  // @ApiResponse({ status: 200, description: 'Election restarted successfully' })
  // @ApiParam({ name: 'stateId', description: 'The ID of the state' })
  // async restartStateElection(@Param('stateId') stateId: string): Promise<{ message: string }> {
  //   this.logger.log(`Restarting election for state ${stateId} for testing`);
  //   await this.stateElectionService.restartElectionsForTesting(stateId);
  //   return { message: `Election for state ${stateId} restarted successfully` };
  // }

  @UseGuards(AuthGuard)
  @Post(':electionId/force-end')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Force end an election (testing)' })
  @ApiResponse({ status: 200, description: 'Election ended successfully' })
  @ApiParam({ name: 'electionId', description: 'The ID of the election' })
  async forceEndElection(@Param('electionId') electionId: string): Promise<StateElection> {
    this.logger.log(`Force ending election ${electionId} for testing`);
    return this.stateElectionService.forceEndElection(electionId);
  }
}
