import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Enum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ParticipateInWarDto {
  @ApiProperty({
    description: 'Amount of energy to spend in battle (1-100)',
    minimum: 1,
    maximum: 100,
    example: 10,
  })
  @IsNumber()
  @Min(0)
  energyAmount: number;

  @ApiProperty({
    description: 'Enable auto mode for the war',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  autoMode: boolean;

  @ApiProperty({
    description: 'Percentage of energy to use in auto mode (1-100)',
    minimum: 1,
    maximum: 100,
    example: 50,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(100)
  autoEnergyPercentage: number;

  @ApiProperty({
    description: 'Side to join in revolution wars (required for revolution wars only)',
    enum: ['attacker', 'defender'],
    example: 'attacker',
    required: false,
  })
  @IsEnum(['attacker', 'defender'])
  @IsOptional()
  side?: 'attacker' | 'defender';
}
