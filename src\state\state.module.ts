import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { State } from './entity/state.entity';
import { StateService } from './state.service';
import { StateController } from './state.controller';
import { Region } from '../region/entity/region.entity';
import { User } from '../user/entity/user.entity';
import { Party } from '../party/entity/party.entity';
import { RegionModule } from '../region/region.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([State, Region, User, Party]),
    RegionModule
  ],
  providers: [StateService],
  controllers: [StateController],
  exports: [StateService],
})
export class StateModule {}
