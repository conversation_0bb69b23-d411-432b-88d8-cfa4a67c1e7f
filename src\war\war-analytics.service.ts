import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { War, WarStatus, WarType, WarTarget } from './entity/war.entity';
import { User } from '../user/entity/user.entity';
import { Region } from '../region/entity/region.entity';

export interface WarStatistics {
  totalWars: number;
  activeWars: number;
  endedWars: number;
  groundWars: number;
  seaWars: number;
  revolutionWars: number;
  conquestWars: number;
  resourceWars: number;
  averageDuration: number; // in hours
  mostActiveRegions: {
    regionId: string;
    regionName: string;
    warCount: number;
  }[];
  mostActiveStates: { stateId: string; stateName: string; warCount: number }[];
}

export interface UserWarStatistics {
  totalParticipation: number;
  warsWon: number;
  warsLost: number;
  totalDamageDealt: number;
  highestDamageInSingleWar: number;
  mostActiveWarId: string;
  currentActiveWars: number;
}

export interface WarTimelineEvent {
  timestamp: Date;
  description: string;
  warId: string;
  warName: string;
  eventType: string;
}

export interface RegionWarHistory {
  regionId: string;
  regionName: string;
  warsAsAttacker: number;
  warsAsDefender: number;
  conquestsWon: number;
  conquestsLost: number;
  resourceWarsWon: number;
  resourceWarsLost: number;
  revolutionsWon: number;
  revolutionsLost: number;
}

@Injectable()
export class WarAnalyticsService {
  constructor(
    @InjectRepository(War)
    private warRepository: Repository<War>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Region)
    private regionRepository: Repository<Region>,
  ) {}

  async getGlobalWarStatistics(): Promise<WarStatistics> {
    const allWars = await this.warRepository.find({
      relations: [
        'attackerRegion',
        'defenderRegion',
        'attackerState',
        'defenderState',
      ],
    });

    const activeWars = allWars.filter(
      (war) =>
        war.status === WarStatus.PENDING ||
        war.status === WarStatus.ENDED ||
        war.status === WarStatus.GROUND_PHASE,
    );

    const endedWars = allWars.filter((war) => war.status === WarStatus.ENDED);

    // Calculate average duration for ended wars
    let totalDurationHours = 0;
    for (const war of endedWars) {
      if (war.endedAt && war.startedAt) {
        const durationMs = war.endedAt.getTime() - war.startedAt.getTime();
        totalDurationHours += durationMs / (1000 * 60 * 60); // Convert to hours
      }
    }
    const averageDuration =
      endedWars.length > 0 ? totalDurationHours / endedWars.length : 0;

    // Count wars by type
    const groundWars = allWars.filter(
      (war) => war.warType === WarType.GROUND,
    ).length;
    const seaWars = allWars.filter((war) => war.warType === WarType.SEA).length;
    const revolutionWars = allWars.filter(
      (war) => war.warType === WarType.REVOLUTION,
    ).length;

    // Count wars by target
    const conquestWars = allWars.filter(
      (war) => war.warTarget === WarTarget.CONQUEST,
    ).length;
    const resourceWars = allWars.filter(
      (war) => war.warTarget === WarTarget.RESOURCES,
    ).length;

    // Find most active regions
    const regionWarCounts = new Map<
      string,
      { regionId: string; regionName: string; warCount: number }
    >();

    for (const war of allWars) {
      if (war.attackerRegion) {
        const regionId = war.attackerRegion.id;
        const regionName = war.attackerRegion.name;

        if (!regionWarCounts.has(regionId)) {
          regionWarCounts.set(regionId, { regionId, regionName, warCount: 0 });
        }

        const regionData = regionWarCounts.get(regionId)!;
        regionData.warCount++;
      }

      if (war.defenderRegion) {
        const regionId = war.defenderRegion.id;
        const regionName = war.defenderRegion.name;

        if (!regionWarCounts.has(regionId)) {
          regionWarCounts.set(regionId, { regionId, regionName, warCount: 0 });
        }

        const regionData = regionWarCounts.get(regionId)!;
        regionData.warCount++;
      }
    }

    const mostActiveRegions = Array.from(regionWarCounts.values())
      .sort((a, b) => b.warCount - a.warCount)
      .slice(0, 5);

    // Find most active states
    const stateWarCounts = new Map<
      string,
      { stateId: string; stateName: string; warCount: number }
    >();

    for (const war of allWars) {
      if (war.attackerState) {
        const stateId = war.attackerState.id;
        const stateName = war.attackerState.name;

        if (!stateWarCounts.has(stateId)) {
          stateWarCounts.set(stateId, { stateId, stateName, warCount: 0 });
        }

        const stateData = stateWarCounts.get(stateId)!;
        stateData.warCount++;
      }

      if (war.defenderState) {
        const stateId = war.defenderState.id;
        const stateName = war.defenderState.name;

        if (!stateWarCounts.has(stateId)) {
          stateWarCounts.set(stateId, { stateId, stateName, warCount: 0 });
        }

        const stateData = stateWarCounts.get(stateId)!;
        stateData.warCount++;
      }
    }

    const mostActiveStates = Array.from(stateWarCounts.values())
      .sort((a, b) => b.warCount - a.warCount)
      .slice(0, 5);

    return {
      totalWars: allWars.length,
      activeWars: activeWars.length,
      endedWars: endedWars.length,
      groundWars,
      seaWars,
      revolutionWars,
      conquestWars,
      resourceWars,
      averageDuration,
      mostActiveRegions,
      mostActiveStates,
    };
  }

  async getUserWarStatistics(userId: number): Promise<UserWarStatistics> {
    const allWars = await this.warRepository.find();

    let totalParticipation = 0;
    let warsWon = 0;
    let warsLost = 0;
    let totalDamageDealt = 0;
    let highestDamageInSingleWar = 0;
    let mostActiveWarId = '';
    let highestDamage = 0;
    let currentActiveWars = 0;

    for (const war of allWars) {
      // Check if user participated in this war
      const attackerParticipant = war.participants?.attackers?.find(
        (p) => p.userId === userId,
      );
      const defenderParticipant = war.participants?.defenders?.find(
        (p) => p.userId === userId,
      );

      if (attackerParticipant || defenderParticipant) {
        totalParticipation++;

        // Calculate total damage dealt
        const damage =
          (attackerParticipant?.damage || 0) +
          (defenderParticipant?.damage || 0);
        totalDamageDealt += damage;

        // Track highest damage in a single war
        if (damage > highestDamage) {
          highestDamage = damage;
          highestDamageInSingleWar = damage;
          mostActiveWarId = war.id;
        }

        // Count active wars
        if (
          war.status === WarStatus.PENDING ||
          war.status === WarStatus.ENDED ||
          war.status === WarStatus.GROUND_PHASE
        ) {
          currentActiveWars++;
        }

        // Count wins and losses
        if (war.status === WarStatus.ENDED) {
          const userSide = attackerParticipant ? 'attacker' : 'defender';
          const attackerWon =
            war.attackerGroundDamage >= war.damageRequirement &&
            war.attackerGroundDamage > war.defenderGroundDamage;

          if (
            (userSide === 'attacker' && attackerWon) ||
            (userSide === 'defender' && !attackerWon)
          ) {
            warsWon++;
          } else {
            warsLost++;
          }
        }
      }
    }

    return {
      totalParticipation,
      warsWon,
      warsLost,
      totalDamageDealt,
      highestDamageInSingleWar,
      mostActiveWarId,
      currentActiveWars,
    };
  }

  async getWarTimeline(limit: number = 10): Promise<WarTimelineEvent[]> {
    const allWars = await this.warRepository.find({
      relations: [
        'attackerRegion',
        'defenderRegion',
        'attackerState',
        'defenderState',
      ],
    });

    const timeline: WarTimelineEvent[] = [];

    for (const war of allWars) {
      // Add war declaration event
      if (war.declaredAt) {
        const attackerName =
          war.attackerState?.name || war.attackerRegion?.name || 'Unknown';
        const defenderName =
          war.defenderState?.name || war.defenderRegion?.name || 'Unknown';

        timeline.push({
          timestamp: war.declaredAt,
          description: `${attackerName} declared war on ${defenderName}`,
          warId: war.id,
          warName: `${attackerName} vs ${defenderName}`,
          eventType: 'declaration',
        });
      }

      // Add war start event
      if (war.startedAt) {
        const attackerName =
          war.attackerState?.name || war.attackerRegion?.name || 'Unknown';
        const defenderName =
          war.defenderState?.name || war.defenderRegion?.name || 'Unknown';

        timeline.push({
          timestamp: war.startedAt,
          description: `War between ${attackerName} and ${defenderName} has begun`,
          warId: war.id,
          warName: `${attackerName} vs ${defenderName}`,
          eventType: 'start',
        });
      }

      // Add sea phase events
      if (war.seaPhaseStartedAt) {
        timeline.push({
          timestamp: war.seaPhaseStartedAt,
          description: `Sea phase has begun`,
          warId: war.id,
          warName: `${war.attackerState?.name || war.attackerRegion?.name || 'Unknown'} vs ${war.defenderState?.name || war.defenderRegion?.name || 'Unknown'}`,
          eventType: 'sea_phase_start',
        });
      }

      // if (war.seaPhaseEndedAt && war.status !== WarStatus.SEA_PHASE) {
      //   timeline.push({
      //     timestamp: war.seaPhaseEndedAt,
      //     description: `Sea phase has ended`,
      //     warId: war.id,
      //     warName: `${war.attackerState?.name || war.attackerRegion?.name || 'Unknown'} vs ${war.defenderState?.name || war.defenderRegion?.name || 'Unknown'}`,
      //     eventType: 'sea_phase_end'
      //   });
      // }

      // Add ground phase events
      // if (war.groundPhaseStartedAt) {
      //   timeline.push({
      //     timestamp: war.groundPhaseStartedAt,
      //     description: `Ground phase has begun`,
      //     warId: war.id,
      //     warName: `${war.attackerState?.name || war.attackerRegion?.name || 'Unknown'} vs ${war.defenderState?.name || war.defenderRegion?.name || 'Unknown'}`,
      //     eventType: 'ground_phase_start'
      //   });
      // }

      // Add war end event
      if (war.endedAt) {
        const attackerName =
          war.attackerState?.name || war.attackerRegion?.name || 'Unknown';
        const defenderName =
          war.defenderState?.name || war.defenderRegion?.name || 'Unknown';
        const attackerWon =
          war.attackerGroundDamage >= war.damageRequirement &&
          war.attackerGroundDamage > war.defenderGroundDamage;

        timeline.push({
          timestamp: war.endedAt,
          description: `War ended. ${attackerWon ? attackerName : defenderName} has won.`,
          warId: war.id,
          warName: `${attackerName} vs ${defenderName}`,
          eventType: 'end',
        });
      }
    }

    // Sort by timestamp (newest first) and limit
    return timeline
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  async getRegionWarHistory(regionId: string): Promise<RegionWarHistory> {
    const allWars = await this.warRepository.find({
      relations: ['attackerRegion', 'defenderRegion'],
    });

    const region = await this.regionRepository.findOne({
      where: { id: regionId },
    });

    if (!region) {
      throw new Error('Region not found');
    }

    let warsAsAttacker = 0;
    let warsAsDefender = 0;
    let conquestsWon = 0;
    let conquestsLost = 0;
    let resourceWarsWon = 0;
    let resourceWarsLost = 0;
    let revolutionsWon = 0;
    let revolutionsLost = 0;

    for (const war of allWars) {
      const isAttacker = war.attackerRegion?.id === regionId;
      const isDefender = war.defenderRegion?.id === regionId;

      if (!isAttacker && !isDefender) {
        continue;
      }

      if (isAttacker) {
        warsAsAttacker++;
      } else {
        warsAsDefender++;
      }

      // Only count ended wars for wins/losses
      if (war.status === WarStatus.ENDED) {
        const attackerWon =
          war.attackerGroundDamage >= war.damageRequirement &&
          war.attackerGroundDamage > war.defenderGroundDamage;

        const regionWon =
          (isAttacker && attackerWon) || (isDefender && !attackerWon);

        if (war.warTarget === WarTarget.CONQUEST) {
          if (regionWon) {
            conquestsWon++;
          } else {
            conquestsLost++;
          }
        } else if (war.warTarget === WarTarget.RESOURCES) {
          if (regionWon) {
            resourceWarsWon++;
          } else {
            resourceWarsLost++;
          }
        } else if (war.warType === WarType.REVOLUTION) {
          if (regionWon) {
            revolutionsWon++;
          } else {
            revolutionsLost++;
          }
        }
      }
    }

    return {
      regionId,
      regionName: region ? region.name : 'Unknown',
      warsAsAttacker,
      warsAsDefender,
      conquestsWon,
      conquestsLost,
      resourceWarsWon,
      resourceWarsLost,
      revolutionsWon,
      revolutionsLost,
    };
  }
}
